<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Modules\Cargo\Entities\Shipment;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Cargo\Http\DataTables\ShipmentsDataTable;
use Modules\Cargo\Http\Requests\ShipmentRequest;
use Modules\Cargo\Entities\ShipmentChat;
use Modules\Cargo\Entities\ShipmentSetting;
use Modules\Cargo\Entities\ClientPackage;
use Modules\Cargo\Entities\Client;
use Modules\Cargo\Entities\Package;
use Modules\Cargo\Entities\Cost;
use Modules\Cargo\Http\Helpers\ShipmentPRNG;
use Modules\Cargo\Http\Helpers\MissionPRNG;
use Modules\Cargo\Entities\PackageShipment;
use Modules\Cargo\Http\Helpers\ShipmentActionHelper;
use Modules\Cargo\Http\Helpers\StatusManagerHelper;
use Modules\Cargo\Http\Helpers\TransactionHelper;
use Modules\Cargo\Entities\Mission;
use Modules\Cargo\Entities\ShipmentMission;
use Modules\Cargo\Entities\ShipmentReason;
use Modules\Cargo\Entities\Country;
use Modules\Cargo\Entities\State;
use Modules\Cargo\Entities\Area;
use Modules\Cargo\Entities\ClientAddress;
use Modules\Cargo\Entities\DeliveryTime;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\BusinessSetting;
use Modules\Cargo\Utility\CSVUtility;
use Modules\Cargo\Http\Helpers\UserRegistrationHelper;
use app\Http\Helpers\ApiHelper;
use App\Http\Resources\ChatResource;
use App\Http\Resources\ShipmentResource;
use App\Models\User;
use Modules\Cargo\Events\AddShipment;
use Modules\Cargo\Events\CreateMission;
use Modules\Cargo\Events\ShipmentAction;
use Modules\Cargo\Events\UpdateMission;
use Modules\Cargo\Events\UpdateShipment;
use Modules\Acl\Repositories\AclRepository;
use Modules\Cargo\Http\Controllers\ClientController;
use Modules\Cargo\Http\Requests\RegisterRequest;
use Auth;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;


class ShipmentsImport implements ToCollection, WithChunkReading, WithHeadingRow
{


    public $match_columns = [];
    public $request = '';
    public function __construct($match_columns , $request)
    {
        $this->match_columns = [];
        $this->request = $request;


        foreach ($match_columns as $key => $value) {
            $this->match_columns[ strtolower( str_replace(' ', '_', strtolower( trim($key) )) )] = $value ;
        }
        // dd($this->match_columns);
        ini_set('memory_limit', '-1');
        set_time_limit(0);
    }
    public function collection(Collection $rows)
    {

        $request = $this->request;
        // $paplt=ProviderAccountsPriceListsType::find(request()->paplt);
        $paplt = '';
        $fails_rows_array_with_errors = [];
        Session::put('date_error',  0);
        $validationRules = [

            'type' => 'required|in:1,2,3,4',
            'branch_id' => 'required',
           // 'shipping_date' => 'required',
            'reciver_name' => 'required',
            'reciver_phone' => 'required|regex:/^01[0125][0-9]{8}$/',
            'reciver_address' => 'required',
            // 'from_country_id' => 'required',
            // 'to_country_id' => 'required',
           // 'from_state_id' => 'required',
           // 'from_area_id' => 'required',


           'to_state_id' => 'required',

            'to_area_id' => 'required',



            'package_id' => 'required',
            'client_id' => 'required', //
            'client_phone' => 'required', //
            'client_address' => 'required', //
            // 'payment_type' => 'required', //
            // 'payment_method_id' => 'required', //




        ];

        $user_role = auth()->user()->role;
        $admin  = 1;
        $auth_staff  = 0;
        $auth_branch = 3;
        $auth_client = 4;

        if ($user_role == $auth_client) {
            $client = Client::where('user_id', auth()->user()->id)->first();
        }



        $valid_flag = 1;
        foreach ($rows as $number => $row) {
            // dd($this->match_columns , $rows);
            try {
                $array = [];

                foreach ($this->match_columns as $key => $value) {

                    $array[$value] = $row[strtolower($key)];
                }

                if ($user_role == $auth_client) {
                    $array['client_id'] = $client->id;
                    $array['client_phone'] = $client->responsible_mobile;
                    $array['client_address'] = $client->addressess->first()?->id ?? 0;
                    $array['from_state_id'] = $client->state_id ?? 0;
                    $array['from_area_id'] = $client->area_id ?? 0;

                }

                if (empty($array['open_shipment'])) {
                    $array['open_shipment'] = 'no';
                }else{
                    $array['open_shipment'] =  $array['open_shipment'] == 'yes' ? 'yes' : 'no';
                }

                if (empty($array['comment'])) {
                    unset($array['comment']);
                }

                $array['package_id'] = Package::first()?->id ?? 0;

                $array['branch_id'] = Branch::first()?->id ?? 0;

                $area = Area::where('name' , 'like' , '%'. $array['to_area_id'] .'%' )->first();

                if(empty($area)){
                    // If area not found, try to find 'other' area in the same city/state
                    // First, check if we have city/state information to work with
                    if (!empty($array['to_state_id'])) {
                        $state = State::find($array['to_state_id']);
                        if ($state) {
                            // Look for 'other' area in the same state
                            $otherArea = Area::where('state_id', $state->id)
                                           ->where('name', 'like', '%other%')
                                           ->first();

                            if ($otherArea) {
                                $area = $otherArea;
                            } else {
                                $valid_flag = 0;
                                $fails_rows_array_with_errors[] = $array;
                                $errors[] = ['to_area_id' => 'Invalid Area: "' . $array['to_area_id'] . '" not found in the specified city. Please add "other" area to this city or use correct area name.'];
                                Session::put('fails_rows_array_with_errors',  $fails_rows_array_with_errors);
                                Session::put('errors2',  $errors);
                                Session::put('date_error',  1);
                            }
                        } else {
                            $valid_flag = 0;
                            $fails_rows_array_with_errors[] = $array;
                            $errors[] = ['to_area_id' => 'Invalid Area: "' . $array['to_area_id'] . '" not found and invalid city/state specified.'];
                            Session::put('fails_rows_array_with_errors',  $fails_rows_array_with_errors);
                            Session::put('errors2',  $errors);
                            Session::put('date_error',  1);
                        }
                    } else {
                        $valid_flag = 0;
                        $fails_rows_array_with_errors[] = $array;
                        $errors[] = ['to_area_id' => 'Invalid Area: "' . $array['to_area_id'] . '" not found and no city/state information provided.'];
                        Session::put('fails_rows_array_with_errors',  $fails_rows_array_with_errors);
                        Session::put('errors2',  $errors);
                        Session::put('date_error',  1);
                    }
                }

                $array['to_area_id'] =  $area ? $area->id : 0;
                $array['to_state_id'] = $area ? $area->state_id : 0;


                $array['open_shipment'] = isset($array['open_shipment']) ? $array['open_shipment'] : 'no' ;

                $array['payment_method_id'] = 'cash_payment';
                $array['payment_type'] = 2;
                $array['from_country_id'] = 65;
                $array['to_country_id'] = 65;
                $array['type'] = 1;

                $validator = Validator::make($array, $validationRules);

                if ($validator->fails() &&  count( $validator->errors()->all()  )  >=  4  ) {
                    //dd($validator->errors()->all() , $array);
                    $valid_flag = 0;
                    $fails_rows_array_with_errors[] = $array;
                    $errors[] = $validator->errors()->all();
                    Session::put('fails_rows_array_with_errors',  $fails_rows_array_with_errors);
                    Session::put('errors2',  $errors);
                    Session::put('date_error',  1);
                    continue;
                }
            } catch (\Throwable $th) {
                //throw $th;
            }


        }


        if ( $valid_flag == 1) {
            foreach ($rows as $number => $row) {

                $array = [];


                foreach ($this->match_columns as $key => $value) {
                    $array[$value] = $row[strtolower($key) ];
                }


                    DB::beginTransaction();

                    if ($user_role == $auth_client) {

                        $array['client_id'] = $client->id;
                        $array['client_phone'] = $client->responsible_mobile;
                        $array['client_address'] = $client->addressess->first()?->id ?? 0;

                        // Use from_state_id from Excel if provided, otherwise use client's state
                        if (empty($array['from_state_id'])) {
                            $array['from_state_id'] = $client->state_id ?? 0;
                        } else {
                            // Validate the from_state_id from Excel
                            $state = State::find($array['from_state_id']);
                            $array['from_state_id'] = $state ? $state->id : ($client->state_id ?? 0);
                        }

                        $array['from_area_id'] = $client->area_id ?? 0;
                    } else {
                        // For non-client users, handle from_state_id from Excel
                        if (!empty($array['from_state_id'])) {
                            $state = State::find($array['from_state_id']);
                            $array['from_state_id'] = $state ? $state->id : 0;
                        } else {
                            $array['from_state_id'] = 0;
                        }
                    }
                    $array['package_id'] = Package::first()?->id ?? 0;

                    $array['branch_id'] = Branch::first()?->id ?? 0;
                    // Handle to_area_id - find area by name
                    $area = Area::where('name' , 'like' , '%'. $array['to_area_id'] .'%' )->first();

                    if (!$area) {
                        // If area not found, try to find 'other' area in the same city/state
                        if (!empty($array['to_state_id'])) {
                            $state = State::find($array['to_state_id']);
                            if ($state) {
                                // Look for 'other' area in the same state
                                $otherArea = Area::where('state_id', $state->id)
                                               ->where('name', 'like', '%other%')
                                               ->first();
                                if ($otherArea) {
                                    $area = $otherArea;
                                }
                            }
                        }
                    }

                    $array['to_area_id'] =  $area ? $area->id : 0;

                    // Handle to_state_id: prioritize Excel value, then area's state, then default
                    if (!empty($array['to_state_id'])) {
                        // Validate the to_state_id from Excel
                        $state = State::find($array['to_state_id']);
                        if ($state) {
                            $array['to_state_id'] = $state->id;
                            // Verify area belongs to this state if both are provided
                            if ($area && $area->state_id != $state->id) {
                                // Log warning but keep Excel state value
                                \Log::warning("Area {$area->id} belongs to state {$area->state_id} but Excel specifies state {$state->id}");
                            }
                        } else {
                            // Invalid state ID from Excel, fallback to area's state
                            $array['to_state_id'] = $area ? $area->state_id : 0;
                        }
                    } else {
                        // No to_state_id in Excel, use area's state
                        $array['to_state_id'] = $area ? $area->state_id : 0;
                    }

                    $array['payment_method_id'] = 'cash_payment';
                    $array['payment_type'] = 2;
                    $array['from_country_id'] = 65;
                    $array['to_country_id'] = 65;

                    if (empty($array['open_shipment'])) {
                        $array['open_shipment'] = 'no';
                    }else{
                        $array['open_shipment'] =  $array['open_shipment'] == 'yes' ? 'yes' : 'no';
                    }

                    if (empty($array['comment'])) {
                        unset($array['comment']);
                    }
                    $array['type'] = 1;

                    try {

                        // Fix: Properly handle package data structure
                        if (isset($array['package_id'])) {
                            $new_package['package_id'] = intval($array['package_id']);
                            unset($array['package_id']);
                        } else {
                            // Use default package if not specified
                            $default_package = Package::first();
                            $new_package['package_id'] = $default_package?->id ?? 1;
                        }

                        // Set default package attributes if not provided
                        $new_package['qty'] = $new_package['qty'] ?? 1;
                        $new_package['weight'] = $new_package['weight'] ?? 1;
                        $new_package['length'] = $new_package['length'] ?? 1;
                        $new_package['width'] = $new_package['width'] ?? 1;
                        $new_package['height'] = $new_package['height'] ?? 1;

                        $array['code'] = -1;
                        $array['status_id'] = 1;
                        $array['payment_method_id'] = 'cash_payment';
                        $request['Shipment'] = $array;


                        $packages[0] = $new_package;
                        $request['Package'] = $packages;

                        $this->storeShipment($request);
                        DB::commit();
                    } catch (\Exception $e) {
                        DB::rollBack();
                    }

            }
        }


    }

    public function chunkSize(): int
    {
        // TODO: Implement chunkSize() method.
        return 1000;
    }


    private function storeShipment($request, $token = null)
    {

        $model = new Shipment();
        $model->fill($request->Shipment);
        $model->code = -1;
        $model->status_id = Shipment::SAVED_STATUS;
        $date = date_create();
        $today = date("Y-m-d");

        if (isset($token)) {

            $user = User::where('remember_token', $token)->first();
            $userClient = Client::where('user_id', $user->id)->first();

            if (isset($user)) {
                $model->client_id = $userClient->id;

                // Validation
                if (!isset($request->Shipment['type']) || !isset($request->Shipment['branch_id']) || !isset($request->Shipment['shipping_date']) || !isset($request->Shipment['client_address']) || !isset($request->Shipment['reciver_name']) || !isset($request->Shipment['reciver_phone']) || !isset($request->Shipment['reciver_address']) || !isset($request->Shipment['from_country_id']) || !isset($request->Shipment['to_country_id']) || !isset($request->Shipment['from_state_id']) || !isset($request->Shipment['to_state_id']) || !isset($request->Shipment['from_area_id']) || !isset($request->Shipment['to_area_id']) || !isset($request->Shipment['payment_method_id']) || !isset($request->Shipment['payment_type']) || !isset($request->Package)) {
                    $message = 'Please make sure to add all required fields';
                    return $message;
                } else {
                    if ($request->Shipment['type'] != Shipment::POSTPAID && $request->Shipment['type'] != Shipment::PREPAID) {
                        return 'Invalid Type';
                    }

                    if (!Branch::find($request->Shipment['branch_id'])) {
                        return 'Invalid Branch';
                    }

                    if (!ClientAddress::where('client_id', $userClient->id)->where('id', $request->Shipment['client_address'])->first()) {
                        return 'Invalid Client Address';
                    }

                    if (!Country::where('covered', 1)->where('id', $request->Shipment['from_country_id'])->first() || !Country::where('covered', 1)->where('id', $request->Shipment['to_country_id'])->first()) {
                        return 'Invalid Country';
                    }

                    if (!State::where('covered', 1)->where('id', $request->Shipment['from_state_id'])->first() || !State::where('covered', 1)->where('id', $request->Shipment['to_state_id'])->first()) {
                        return 'Invalid State';
                    }

                    if (!Area::where('state_id', $request->Shipment['from_state_id'])->where('id', $request->Shipment['from_area_id'])->first() || !Area::where('state_id', $request->Shipment['to_state_id'])->where('id', $request->Shipment['to_area_id'])->first()) {
                        return 'Invalid Area';
                    }

                    if (isset($request->Shipment['payment_method_id'])) {
                        $paymentSettings = resolve(\Modules\Payments\Entities\PaymentSetting::class)->toArray();
                        if (!isset($paymentSettings[$request->Shipment['payment_method_id']])) {
                            return 'Invalid Payment Method Id';
                        }
                    }

                    if ($request->Shipment['payment_type'] != Shipment::POSTPAID && $request->Shipment['payment_type'] != Shipment::PREPAID) {
                        return 'Invalid Payment Type';
                    }

                    // if(isset($request->Shipment['delivery_time'])){
                    //     $delivery_time = DeliveryTime::where('id', $request->Shipment['delivery_time'] )->first();
                    //     if(!$delivery_time){
                    //         return 'Invalid Delivery Time';
                    //     }
                    // }

                }

                if (!isset($request->Shipment['client_phone'])) {
                    $model->client_phone = $userClient->responsible_mobile;
                }

                if (!isset($request->Shipment['amount_to_be_collected'])) {
                    $model->amount_to_be_collected = 0;
                }
            } else {
                return response()->json(['message' => 'invalid or Expired Api Key']);
            }
        }

      //  dd($model , $request->Shipment );


        if (!$model->save()) {
            return response()->json(['message' => new \Exception()]);
        }

        if (ShipmentSetting::getVal('def_shipment_code_type') == 'random') {
            $barcode = ShipmentPRNG::get();
        } else {
            $code = '';
            for ($n = 0; $n < ShipmentSetting::getVal('shipment_code_count'); $n++) {
                $code .= '0';
            }
            $code       =   substr($code, 0, -strlen($model->id));
            $barcode    =   $code . $model->id;
        }
        $model->barcode = $barcode;
        $model->code = ShipmentSetting::getVal('shipment_prefix') . $barcode;

        if (auth()->user() && auth()->user()->role == 4) { // IF IN AUTH USER == CLIENT
            $client = Client::where('user_id', auth()->user()->id)->first();
            $model->client_id = $client->id;
        }

        if (!$model->save()) {
            return response()->json(['message' => new \Exception()]);
        }

        $costs = $this->applyShipmentCost($model, $request->Package);

        $model->fill($costs);
        if (!$model->save()) {
            return response()->json(['message' => new \Exception()]);
        }

        $counter = 0;
        if (isset($request->Package)) {
            if (!empty($request->Package)) {

                if (isset($request->Package[$counter]['package_id'])) {

                    if (isset($token)) {
                        $total_weight = 0;
                    }

                    foreach ($request->Package as $package) {
                        if (isset($token)) {
                            if (!Package::find($package['package_id'])) {
                                return 'Package invalid';
                            }

                            if (!isset($package['qty'])) {
                                $package['qty'] = 1;
                            }

                            if (!isset($package['weight'])) {
                                $package['weight'] = 1;
                            }
                            if (!isset($package['length'])) {
                                $package['length'] = 1;
                            }
                            if (!isset($package->width)) {
                                $package['width'] = 1;
                            }
                            if (!isset($package['height'])) {
                                $package['height'] = 1;
                            }

                            $total_weight = $total_weight + $package['weight'];
                        }
                        $package_shipment = new PackageShipment();
                        $package_shipment->fill($package);
                        $package_shipment->shipment_id = $model->id;
                        if (!$package_shipment->save()) {
                            throw new \Exception();
                        }
                    }

                    if (isset($token)) {
                        $model->total_weight = $total_weight;
                        if (!$model->save()) {
                            return response()->json(['message' => new \Exception()]);
                        }
                    }
                }
            }
        }

        if (isset($token)) {
            $message = 'Shipment added successfully';
            return $message;
        } else {
            return $model;
        }
    }



    public function applyShipmentCost($request, $packages)
    {
        $client_costs    = Client::where('id', $request['client_id'])->first();

        // Fix: Only update from_state_id and from_area_id if not already set from Excel
        $update_data = [];
        if (empty($request['from_state_id'])) {
            $update_data['from_state_id'] = $client_costs?->state_id ?? 0;
        }
        if (empty($request['from_area_id'])) {
            $update_data['from_area_id'] = $client_costs?->area_id ?? 0;
        }

        if (!empty($update_data)) {
            Shipment::where('id', $request['id'])->update($update_data);
        }


        $idPackages      = array_column($packages, 'package_id');
        $client_packages = ClientPackage::where('client_id', $request['client_id'])->whereIn('package_id', $idPackages)->get();

        $from_country_id = $request['from_country_id'];
        $to_country_id = $request['to_country_id'];

        if (isset($request['from_state_id']) && isset($request['to_state_id'])) {
            $from_state_id = $request['from_state_id'];
            $to_state_id = $request['to_state_id'];
        }
        if (isset($request['from_area_id']) && isset($request['to_area_id'])) {
            $from_area_id = $request['from_area_id'];
            $to_area_id = $request['to_area_id'];
        }

        $total_weight = 0;
        $package_extras = 0;

        if ($client_packages) {
            foreach ($client_packages as $pack) {
                $total_weight += isset($pack['weight']) ? $pack['weight'] : 1;
                $extra = $pack['cost'];
                $package_extras += $extra;
            }
        } else {
            foreach ($packages as $pack) {
                $total_weight += isset($pack['weight']) ? $pack['weight'] : 1;
                $extra = Package::find($pack['package_id'])->cost;
                $package_extras += $extra;
            }
        }

        //$weight =  $request['total_weight'];
        $weight = isset($request['total_weight']) ? $request['total_weight'] : $total_weight;

        $array = ['return_cost' => 0, 'shipping_cost' => 0, 'tax' => 0, 'insurance' => 0];

        // Use client-specific costs if available, otherwise use default state costs
        $clientId = $request['client_id'] ?? null;

        if ($clientId && $to_state_id) {
            $costs = \Modules\Cargo\Entities\ClientStateCost::getClientStateCosts($clientId, $to_state_id);

            // Calculate shipping cost using client-specific or default costs
            $base_shipping_cost = $costs['def_shipping_cost'] ?: 0;
            $shipping_cost_per_kg = $costs['def_shipping_cost_gram'] ?: 0;

            // Calculate return cost using client-specific or default costs
            $base_return_cost = $costs['def_return_cost'] ?: 0;
            $return_cost_per_kg = $costs['def_return_cost_gram'] ?: 0;

            // Calculate costs (first 3kg base cost + additional weight)
            $base_weight = 3;
            $extra_weight = max(0, $weight - $base_weight);

            $array['shipping_cost'] = $base_shipping_cost + ($extra_weight * $shipping_cost_per_kg);
            $array['return_cost'] = $base_return_cost + ($extra_weight * $return_cost_per_kg);

            return $array;
        }

        // Fallback to old cost calculation if no client-specific costs
        $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);

        if (isset($request['from_area_id']) && isset($request['to_area_id'])) {
            $covered_cost = $covered_cost->where('from_area_id', $from_area_id)->where('to_area_id', $to_area_id);
            if (!$covered_cost->first()) {
                $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);

                if (isset($request['from_state_id']) && isset($request['to_state_id'])) {
                    $covered_cost = $covered_cost->where('from_state_id', $from_state_id)->where('to_state_id', $to_state_id);
                    if (!$covered_cost->first()) {
                        $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);
                        $covered_cost = $covered_cost->where('from_state_id', 0)->where('to_state_id', 0);
                    }
                } else {
                    $covered_cost = $covered_cost->where('from_area_id', 0)->where('to_area_id', 0);
                    if (!$covered_cost->first()) {
                        $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);
                        $covered_cost = $covered_cost->where('from_state_id', 0)->where('to_state_id', 0);
                    }
                }
            }
        } else {

            if (isset($request['from_state_id']) && isset($request['to_state_id'])) {
                $covered_cost = $covered_cost->where('from_state_id', $from_state_id)->where('to_state_id', $to_state_id);
            } else {
                $covered_cost = $covered_cost->where('from_area_id', 0)->where('to_area_id', 0);
                if (!$covered_cost->first()) {
                    $covered_cost = Cost::where('from_country_id', $from_country_id)->where('to_country_id', $to_country_id);
                    $covered_cost = $covered_cost->where('from_state_id', 0)->where('to_state_id', 0);
                }
            }
        }
        $covered_cost = $covered_cost->first();

        // Get state-based costs instead of client-based costs
        $state_costs = null;
        if (isset($request['to_state_id']) && $request['to_state_id']) {
            $state_costs = \Modules\Cargo\Entities\State::find($request['to_state_id']);
        }

        $def_return_cost_gram = $state_costs && $state_costs->def_return_cost_gram !== null ? $state_costs->def_return_cost_gram : ShipmentSetting::getCost('def_return_cost_gram');
        $def_return_cost      = $state_costs && $state_costs->def_return_cost !== null ? $state_costs->def_return_cost : ShipmentSetting::getCost('def_return_cost');

        $def_shipping_cost_gram = $state_costs && $state_costs->def_shipping_cost_gram !== null ? $state_costs->def_shipping_cost_gram : ShipmentSetting::getCost('def_shipping_cost_gram');
        $def_shipping_cost      = $state_costs && $state_costs->def_shipping_cost !== null ? $state_costs->def_shipping_cost : ShipmentSetting::getCost('def_shipping_cost');

        // Use global settings for other costs (tax, insurance, mile costs)
        $def_return_mile_cost_gram = ShipmentSetting::getCost('def_return_mile_cost_gram');
        $def_return_mile_cost      = ShipmentSetting::getCost('def_return_mile_cost');
        $def_mile_cost_gram        = ShipmentSetting::getCost('def_mile_cost_gram');
        $def_mile_cost             = ShipmentSetting::getCost('def_mile_cost');
        $def_insurance_gram        = ShipmentSetting::getCost('def_insurance_gram');
        $def_insurance             = ShipmentSetting::getCost('def_insurance');
        $def_tax_gram              = ShipmentSetting::getCost('def_tax_gram');
        $def_tax                   = ShipmentSetting::getCost('def_tax');




        if ($covered_cost != null) {
            if ($weight > 1) {
                if (ShipmentSetting::getVal('is_def_mile_or_fees') == '2') {
                    $return_cost = (float) $def_return_cost ?? $covered_cost->return_cost + (float) ($def_return_cost_gram * ($weight - 1));
                    $shipping_cost_first_one = (float) ($def_shipping_cost != null ? $def_shipping_cost : $covered_cost->shipping_cost) + $package_extras;
                    $shipping_cost_for_extra = (float) ($def_shipping_cost_gram * ($weight - 1));
                } else if (ShipmentSetting::getVal('is_def_mile_or_fees') == '1') {
                    $return_cost = (float) $def_return_mile_cost ?? $covered_cost->return_mile_cost + (float) ($def_return_mile_cost_gram * ($weight - 1));
                    $shipping_cost_first_one = (float) ($def_mile_cost ?? $covered_cost->mile_cost) + $package_extras;
                    $shipping_cost_for_extra = (float) ($def_mile_cost_gram * ($weight - 1));
                }
                $insurance = (float) $def_insurance ?? $covered_cost->insurance + (float) ($def_insurance_gram * ($weight - 1));

                $tax_for_first_one = (($def_tax ?? $covered_cost->tax * $shipping_cost_first_one) / 100);

                $tax_for_exrea = (($def_tax_gram * $shipping_cost_for_extra) / 100);

                $shipping_cost = $shipping_cost_first_one + $shipping_cost_for_extra;
                $tax = $tax_for_first_one + $tax_for_exrea;
            } else {

                if (ShipmentSetting::getVal('is_def_mile_or_fees') == '2') {

                    $return_cost = (float) $def_return_cost ?? $covered_cost->return_cost;
                    $shipping_cost = (float) ($def_shipping_cost != null ? $def_shipping_cost : $covered_cost->shipping_cost) + $package_extras;
                } else if (ShipmentSetting::getVal('is_def_mile_or_fees') == '1') {
                    $return_cost = (float) $def_return_mile_cost ?? $covered_cost->return_mile_cost;
                    $shipping_cost = (float) ($def_mile_cost ?? $covered_cost->mile_cost) + $package_extras;
                }
                $insurance = (float) $def_insurance ?? $covered_cost->insurance;
                $tax = (($def_tax ?? $covered_cost->tax * $shipping_cost) / 100);
            }

            $array['tax'] = $tax;
            $array['insurance'] = $insurance;
            $array['return_cost'] = $return_cost;
            $array['shipping_cost'] = $shipping_cost;
        } else {
            if ($weight > 1) {
                if (ShipmentSetting::getVal('is_def_mile_or_fees') == '2') {
                    $return_cost = $def_return_cost + (float) ($def_return_cost_gram * ($weight - 1));
                    $shipping_cost_first_one = $def_shipping_cost + $package_extras;
                    $shipping_cost_for_extra = (float) ($def_shipping_cost_gram * ($weight - 1));
                } else if (ShipmentSetting::getVal('is_def_mile_or_fees') == '1') {
                    $return_cost = $def_return_mile_cost + (float) ($def_return_mile_cost_gram * ($weight - 1));
                    $shipping_cost_first_one = $def_mile_cost + $package_extras;
                    $shipping_cost_for_extra = (float) ($def_mile_cost_gram * ($weight - 1));
                }

                $insurance = $def_insurance + (float) ($def_insurance_gram * ($weight - 1));
                $tax_for_first_one = (($def_tax * $shipping_cost_first_one) / 100);
                $tax_for_exrea = ((ShipmentSetting::getCost('def_tax_gram') * $shipping_cost_for_extra) / 100);

                $shipping_cost = $shipping_cost_first_one + $shipping_cost_for_extra;
                // dd($shipping_cost_first_one ,$shipping_cost_for_extra  );

                $tax = $tax_for_first_one + $tax_for_exrea;
            } else {
                if (ShipmentSetting::getVal('is_def_mile_or_fees') == '2') {
                    $return_cost = $def_return_cost;
                    $shipping_cost = $def_shipping_cost + $package_extras;
                } else if (ShipmentSetting::getVal('is_def_mile_or_fees') == '1') {
                    $return_cost = $def_return_mile_cost;
                    $shipping_cost = $def_mile_cost + $package_extras;
                }
                $insurance = $def_insurance;
                $tax = (($def_tax * $shipping_cost) / 100);
            }

            $array['tax'] = $tax;
            $array['insurance'] = $insurance;
            $array['return_cost'] = $return_cost;
            $array['shipping_cost'] = $shipping_cost;
        }
        return $array;
    }

}
